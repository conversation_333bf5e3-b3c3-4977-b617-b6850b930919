# 筛选条件参数传递Bug修复测试

## 修复内容
修复了前端调用`get_metrics_decomposition`接口时，筛选条件参数没有正确传递给后端的bug。

## 修复前的问题
- 前端在调用`get_metrics_decomposition`接口时，只从URL参数中获取筛选条件
- 用户在筛选条件中选择的最新数据（如子品牌、省份、城市、零售商、平台、商品等）没有传递给后端
- 导致指标拆解功能返回的数据不准确

## 修复后的改进
1. **App.js**: 在`ResultTable`组件调用中添加`filterConditions` prop，传递当前筛选条件状态
2. **ResultTable.js**: 
   - 添加`filterConditions`参数接收
   - 在`MetricsDecompositionModal`中添加`filterConditions`参数
   - 修改`fetchDecompositionData`函数优先使用传递的筛选条件
   - 更新useCallback依赖项

## 测试步骤
1. 启动前端和后端服务
2. 在筛选条件中选择特定的子品牌、省份、城市等
3. 进行归因分析，确保选择了指标归因
4. 在结果表格中点击"查看指标拆解"
5. 验证指标拆解弹窗中的数据是否基于当前选择的筛选条件

## 预期结果
- 指标拆解功能应该基于用户当前选择的筛选条件返回正确的数据
- 不再依赖URL参数，而是使用最新的筛选条件状态
- 筛选条件变化时，指标拆解数据会相应更新

## 技术细节
- 筛选条件通过props层层传递：App.js → ResultTable → MetricsDecompositionModal
- 使用`filterConditions?.subBrand || urlParams.get('sub_brand') || '全部'`的模式确保向后兼容
- 添加了活动GMV相关的筛选条件支持（券机制、券门槛、优惠力度）
